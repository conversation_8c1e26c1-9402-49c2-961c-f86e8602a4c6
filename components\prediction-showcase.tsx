"use client"

import { useEffect, useState } from "react"
import PredictionCard from "./prediction-card"
import TrophyIcon from "./svg/trophy-icon"
import DiagramIcon from "./svg/diagram-icon"
import ArrowIcon from "./svg/arrow-icon"
import MessageIcon from "./svg/message-icon"

export default function PredictionShowcase() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const element = document.getElementById('prediction-showcase')
    if (element) {
      observer.observe(element)
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [])

  return (
    <section id="prediction-showcase" className="w-full py-20 md:py-28 relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px] parallax-element"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "60%",
          right: "40px",
          transform: "translateY(-50%) rotate(-164.56deg)",
          background: "#2CAD9C",
          zIndex: "0",
        }}
      />

      <div className="container mx-auto px-4 max-w-[1200px] relative z-10">
        <div className="text-center mb-6">
        <h2 className={`text-center text-[28px] md:text-5xl font-bold mb-4 transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`}>
          <span className="gradient-text-animated hover:scale-105 transition-transform duration-300 cursor-default">
            DarkHorse Your Picks
          </span>
        </h2>
        </div>

        <div className={`text-center mb-20 max-w-3xl mx-auto transition-all duration-700 ${isVisible ? 'text-reveal-delay-1' : 'opacity-0'}`}>
          <p className="text-white text-[18px] md:text-2xl mb-2 hover-zone">Unlock Exclusive AI-Powered Insights</p>
          <p className="text-[#FFFFFF80] text-[14px] inline-block px-3 py-1 rounded-full bg-[#28282847] backdrop-blur-[20px] hover-zone">
            (auto-updating feed for upcoming game data)
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-center justify-between mb-20">
          <div className={`w-full lg:w-1/2 mb-10 lg:mb-0 text-left transition-all duration-700 ${isVisible ? 'slide-in-left' : 'opacity-0'}`}>
            <div className="grid grid-cols-2 gap-8 md:hidden">
              <div className="flex flex-col items-center text-center hover-zone group">
                <TrophyIcon className="h-10 w-10 mb-4 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
                <h3 className="text-white text-base font-medium group-hover:text-[#19FB9B] transition-colors duration-300">Predicted Winner</h3>
                <h3 className="text-white text-base font-medium group-hover:text-[#19FB9B] transition-colors duration-300">& Confidence Score</h3>
              </div>

              <div className="flex flex-col items-center text-center hover-zone group">
                <DiagramIcon className="h-10 w-10 mb-4 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
                <h3 className="text-white text-base font-medium group-hover:text-[#19FB9B] transition-colors duration-300">Moneyline</h3>
                <h3 className="text-white text-base font-medium group-hover:text-[#19FB9B] transition-colors duration-300">& Spread Forecasts</h3>
              </div>

              <div className="flex flex-col items-center text-center hover-zone group">
                <ArrowIcon className="h-10 w-10 mb-4 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
                <h3 className="text-white text-base font-medium group-hover:text-[#19FB9B] transition-colors duration-300">Total Score</h3>
                <h3 className="text-white text-base font-medium group-hover:text-[#19FB9B] transition-colors duration-300">(Over/Under) Projection</h3>
              </div>

              <div className="flex flex-col items-center text-center hover-zone group">
                <MessageIcon className="h-10 w-10 mb-4 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
                <h3 className="text-white text-base font-medium group-hover:text-[#19FB9B] transition-colors duration-300">AI Chatbot:</h3>
                <p className="text-white text-sm group-hover:text-[#19FB9B] transition-colors duration-300">Ask for stats, trends, or matchup analysis — instantly</p>
              </div>
            </div>

            <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-16">
              <div className="hover-zone group">
                <TrophyIcon className="h-8 w-8 mb-4 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
                <h3 className="text-white text-xl font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">Predicted Winner</h3>
                <h3 className="text-white text-xl font-medium group-hover:text-[#19FB9B] transition-colors duration-300">& Confidence Score</h3>
              </div>

              <div className="hover-zone group">
                <DiagramIcon className="h-8 w-8 mb-4 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
                <h3 className="text-white text-xl font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">Moneyline</h3>
                <h3 className="text-white text-xl font-medium group-hover:text-[#19FB9B] transition-colors duration-300">& Spread Forecasts</h3>
              </div>

              <div className="hover-zone group">
                <ArrowIcon className="h-8 w-8 mb-4 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
                <h3 className="text-white text-xl font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">Total Score</h3>
                <h3 className="text-white text-xl font-medium group-hover:text-[#19FB9B] transition-colors duration-300">(Over/Under) Projection</h3>
              </div>

              <div className="hover-zone group">
                <MessageIcon className="h-8 w-8 mb-4 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
                <h3 className="text-white text-xl font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">AI Chatbot:</h3>
                <p className="text-white text-lg group-hover:text-[#19FB9B] transition-colors duration-300">Ask for stats, trends, or matchup analysis — instantly</p>
              </div>
            </div>
          </div>

          <div className={`w-full lg:w-1/2 transition-all duration-700 ${isVisible ? 'slide-in-right' : 'opacity-0'}`}>
            <div className="card-3d hover-zone">
              <div className="card-3d-inner">
                <PredictionCard />
              </div>
            </div>
          </div>
        </div>

        <div className={`mt-20 text-center transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`}>
          <p className="text-white text-xl md:text-2xl mb-6 hover-zone">Know Before You Act – Unbiased, Data-Driven Analytics</p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 w-full">
          <button className="magnetic-button liquid-button prediction-button text-white text-[16px] sm:text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[56px] sm:h-[48px] flex items-center justify-center relative overflow-hidden group">
            <span className="relative z-10">Get AI Predictions</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
          <button className="magnetic-button border border-white/20 bg-transparent text-white text-[16px] sm:text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[56px] sm:h-[48px] flex items-center justify-center hover:bg-white/10 hover:border-[#19FB9B]/50 transition-all duration-300 relative overflow-hidden group">
            <span className="relative z-10">Sign Up for Exclusive Access</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#19FB9B]/10 to-[#8C01FA]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
        </div>
      </div>
    </section>
  )
}


