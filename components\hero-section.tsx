"use client"

import Image from "next/image"
import { useEffect, useState } from "react"

export default function HeroSection() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <section className="w-full py-8 md:py-24 relative overflow-hidden">
      <div className="container mx-auto text-center px-4 relative z-20">
        <div className="flex justify-center mb-6 md:mb-8">
          <div className={`logo-binary-container w-[100px] h-[100px] md:w-[155px] md:h-[155px] hover-zone glow-pulse transition-all duration-500 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`}>
            {/* Base logo with reduced opacity */}
            <Image
              src="/white_horse_icon.svg"
              alt="DarkHorse Icon"
              width={72}
              height={78}
              className="logo-base object-contain w-full h-full transition-all duration-300 hover:scale-110"
            />

            {/* Binary code overlay that flows through the logo shape */}
            <div className="logo-binary-overlay">
              <div className="binary-stream">1010110011010101100110101011001101010110011010101100110101011001</div>
              <div className="binary-stream">0101101101011011010110110101101101011011010110110101101101011011</div>
              <div className="binary-stream">1101010010110110101001011011010100101101101010010110110101001011</div>
              <div className="binary-stream">0110101110101011010111010101101011101010110101110101011010111010</div>
              <div className="binary-stream">1010110011010101100110101011001101010110011010101100110101011001</div>
              <div className="binary-stream">0101101101011011010110110101101101011011010110110101101101011011</div>
              <div className="binary-stream">1
1
0
0
1
1
0
0
0
1
1
0
0
1
1
1
0
0
1
1
0
0
0
1
1
0
0
1
1
1
0
0
1
1
0
0
0
1
1
0</div>
              <div className="binary-stream">0
0
1
1
1
1
0
1
1
0
0
0
0
1
0
0
1
1
1
1
0
1
1
0
0
0
0
1
0
0
1
1
1
1
0
1
1
0
0
0</div>
              <div className="binary-stream">1
1
1
1
0
0
0
0
0
0
1
1
1
1
1
1
0
0
0
0
0
0
1
1
1
1
1
1
0
0
0
0
0
0
1
1
1
1
1
1</div>
              <div className="binary-stream">0
0
0
0
1
1
1
1
1
1
0
0
0
0
0
0
1
1
1
1
1
1
0
0
0
0
0
0
1
1
1
1
1
1
0
0
0
0
0
0</div>
              <div className="binary-stream">1
0
1
1
0
1
0
0
1
0
1
1
0
1
0
0
1
0
1
1
0
1
0
0
1
0
1
1
0
1
0
0
1
0
1
1
0
1
0
0</div>
              <div className="binary-stream">0
1
0
0
1
0
1
1
0
1
0
0
1
0
1
1
0
1
0
0
1
0
1
1
0
1
0
0
1
0
1
1
0
1
0
0
1
0
1
1</div>
              <div className="binary-stream">1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0</div>
              <div className="binary-stream">0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1</div>
              <div className="binary-stream">1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1</div>
              <div className="binary-stream">0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0</div>
              <div className="binary-stream">1
1
0
1
0
1
1
0
1
0
1
1
0
1
0
1
1
0
1
0
1
1
0
1
0
1
1
0
1
0
1
1
0
1
0
1
1
0
1
0</div>
              <div className="binary-stream">0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1</div>
              <div className="binary-stream">1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0</div>
              <div className="binary-stream">0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1</div>
              <div className="binary-stream">1
1
0
0
1
0
1
0
1
1
0
0
1
0
1
0
1
1
0
0
1
0
1
0
1
1
0
0
1
0
1
0
1
1
0
0
1
0
1
0</div>
              <div className="binary-stream">0
0
1
1
0
1
0
1
0
0
1
1
0
1
0
1
0
0
1
1
0
1
0
1
0
0
1
1
0
1
0
1
0
0
1
1
0
1
0
1</div>
              <div className="binary-stream">1
0
0
1
1
1
0
0
1
0
0
1
1
1
0
0
1
0
0
1
1
1
0
0
1
0
0
1
1
1
0
0
1
0
0
1
1
1
0
0</div>
              <div className="binary-stream">0
1
1
0
0
0
1
1
0
1
1
0
0
0
1
1
0
1
1
0
0
0
1
1
0
1
1
0
0
0
1
1
0
1
1
0
0
0
1
1</div>
              <div className="binary-stream">1
0
1
1
0
1
0
1
1
0
1
1
0
1
0
1
1
0
1
1
0
1
0
1
1
0
1
1
0
1
0
1
1
0
1
1
0
1
0
1</div>
              <div className="binary-stream">0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0</div>
              <div className="binary-stream">1
1
1
0
0
1
1
1
0
0
1
1
1
0
0
1
1
1
0
0
1
1
1
0
0
1
1
1
0
0
1
1
1
0
0
1
1
1
0
0</div>
              <div className="binary-stream">0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1
0
0
1
0
1</div>
              <div className="binary-stream">1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1
0
1
1
0
0
1</div>
              <div className="binary-stream">0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1</div>
              <div className="binary-stream">1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0</div>
              <div className="binary-stream">0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1
1
1
0
0
0
1</div>
              <div className="binary-stream">1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1</div>
              <div className="binary-stream">0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1
0
1</div>
              <div className="binary-stream">1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0
0
1
1
0
1
0</div>
            </div>
          </div>
        </div>

        <h1 className={`text-[28px] md:text-6xl font-bold mb-3 md:mb-4 px-2 transition-all duration-700 ${isVisible ? 'text-reveal' : 'opacity-0'}`}>
          <span
            className="hover-zone transition-all duration-300"
            style={{
              background:
                "linear-gradient(0deg, #FFFFFF, #FFFFFF)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
            }}
          >
            DarkHorse Your Picks
          </span>
          <br />
          for an <span className="gradient-text-animated hover:scale-105 transition-transform duration-300">Unstoppable Edge</span>
        </h1>

        <p className={`text-lg md:text-2xl mb-6 md:mb-10 max-w-3xl mx-auto transition-all duration-700 ${isVisible ? 'text-reveal-delay-1' : 'opacity-0'}`}>
          AI-Powered Sports Predictions for Smarter Decision-Making
        </p>

        <div className={`flex flex-row justify-center flex-wrap gap-3 mb-8 md:mb-12 transition-all duration-700 ${isVisible ? 'text-reveal-delay-2' : 'opacity-0'}`}>
          <div className="bg-[#28282847] backdrop-blur-[20px] rounded-full px-6 py-2 hover-zone morph-shape transition-all duration-300 hover:bg-[#28282860]">
            <span className="text-[#99ACEA] font-['Inter'] text-[14px] tracking-[0%]">100% Data-Driven Insights</span>
          </div>
          <div className="bg-[#28282847] backdrop-blur-[20px] rounded-full px-6 py-2 hover-zone morph-shape transition-all duration-300 hover:bg-[#28282860]" style={{ animationDelay: '2s' }}>
            <span className="text-[#99ACEA] font-['Inter'] text-[14px] tracking-[0%]">Proven Accuracy</span>
          </div>
          <div className="bg-[#28282847] backdrop-blur-[20px] rounded-full px-6 py-2 hover-zone morph-shape transition-all duration-300 hover:bg-[#28282860]" style={{ animationDelay: '4s' }}>
            <span className="text-[#99ACEA] font-['Inter'] text-[14px] tracking-[0%]">Real-Time Updates</span>
          </div>
        </div>

        <div className={`transition-all duration-700 ${isVisible ? 'text-reveal-delay-3' : 'opacity-0'}`}>
          <button className="magnetic-button liquid-button bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[14px] rounded-[88px] w-full max-w-xs md:w-[224px] h-[48px] px-[8px] py-[8px] flex items-center justify-center gap-[8px] mb-8 md:mb-16 mx-auto font-medium relative overflow-hidden group">
            <span className="relative z-10">Get AI Predictions</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>

        <div className={`max-w-5xl mx-auto mb-8 md:mb-16 flex flex-col md:flex-row items-center justify-center gap-8 transition-all duration-700 ${isVisible ? 'text-reveal-delay-4' : 'opacity-0'}`}>
          <div className="card-3d hover-zone group">
            <div className="card-3d-inner">
              <Image
                src="/Macbook.webp"
                alt="DarkHorse Desktop App"
                width={1600}
                height={900}
                className="w-full max-w-[1600px] h-auto rounded-lg shadow-lg transition-all duration-500 group-hover:shadow-2xl group-hover:shadow-[#19FB9B]/20"
              />
            </div>
          </div>
        </div>

        <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 max-w-6xl mx-auto transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`}>
          <div className="card-3d bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-auto md:h-[118px] py-4 px-4 md:pt-[31px] md:pr-[30px] md:pb-[31px] md:pl-[30px] flex items-center justify-center mx-auto hover-zone group transition-all duration-300">
            <div className="card-3d-inner">
              <h3 className="text-[16px] md:text-[20px] font-medium text-white text-center group-hover:text-[#19FB9B] transition-colors duration-300">
                NFL, NBA, MLB & MLS
                <br />
                Predictions
              </h3>
            </div>
          </div>

          <div className="card-3d bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-auto md:h-[118px] py-4 px-4 md:pt-[31px] md:pr-[30px] md:pb-[31px] md:pl-[30px] flex items-center justify-center mx-auto hover-zone group transition-all duration-300" style={{ animationDelay: '0.2s' }}>
            <div className="card-3d-inner">
              <h3 className="text-[16px] md:text-[20px] font-medium text-white text-center group-hover:text-[#19FB9B] transition-colors duration-300">
                Win Probabilities, Spreads,
                <br />& Totals Forecasts
              </h3>
            </div>
          </div>

          <div className="card-3d bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-auto md:h-[118px] py-4 px-4 md:pt-[31px] md:pr-[30px] md:pb-[31px] md:pl-[30px] flex items-center justify-center mx-auto hover-zone group transition-all duration-300" style={{ animationDelay: '0.4s' }}>
            <div className="card-3d-inner">
              <h3 className="text-[16px] md:text-[20px] font-medium text-white text-center group-hover:text-[#19FB9B] transition-colors duration-300">
                AI-Generated Analysis with
                <br />
                No Bookmaker Bias
              </h3>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}






