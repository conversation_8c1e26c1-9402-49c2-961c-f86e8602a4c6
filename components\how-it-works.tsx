"use client"

import Image from "next/image"
import { ChevronLeft, ChevronDown, Star, Bell } from "lucide-react"
import { useEffect, useState } from "react"
import Nuggets<PERSON>ogo from "./svg/nuggets-logo"
import PacersLogo from "./svg/pacers-logo"

export default function HowItWorks() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const element = document.getElementById('how-it-works')
    if (element) {
      observer.observe(element)
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [])

  return (
    <section id="how-it-works" className="py-16 md:py-24 bg-[#050816] relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px] parallax-element parallax-slow"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "60%",
          right: "50px",
          transform: "translateY(-50%) rotate(-164.56deg)",
          background: "linear-gradient(45deg, #2CAD9C, #19FB9B)",
          zIndex: "0",
          animation: "floatBlur1 16s ease-in-out infinite, gradientWave 22s ease infinite",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px] parallax-element parallax-fast"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "30%",
          left: "50px",
          transform: "translateY(-50%) rotate(-164.56deg)",
          background: "linear-gradient(45deg, #7F20EF, #8C01FA)",
          zIndex: "0",
          animation: "floatBlur2 18s ease-in-out infinite, gradientWave 28s ease infinite",
        }}
      />

      <div className="container mx-auto px-4 max-w-[1200px] relative z-10">
        <h2 className={`text-center text-4xl md:text-5xl font-bold mb-4 transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`}>
          <span className="gradient-text-animated hover:scale-105 transition-transform duration-300 cursor-default">
            How It Works
          </span>
        </h2>

        <p className={`text-center text-white text-[16px] sm:text-[18px] md:text-xl mb-16 hover-zone transition-all duration-700 ${isVisible ? 'text-reveal-delay-1' : 'opacity-0'}`}>AI-Powered Predictions Made Simple</p>

        <div className={`flex flex-col lg:flex-row items-center justify-between gap-12 transition-all duration-700 ${isVisible ? 'slide-in-left' : 'opacity-0'}`}>
          <div className="w-full lg:w-1/2 text-center lg:text-left order-1 lg:order-2 hover-zone group">
            <h3 className="text-white text-[18px] sm:text-[24px] md:text-3xl font-bold mb-4 group-hover:text-[#19FB9B] transition-colors duration-300">Select a Game</h3>
            <p className="text-[#9ca2b5] text-[14px] sm:text-[16px] md:text-lg group-hover:text-white transition-colors duration-300">Browse upcoming matchups & <br /> AI-generated insights.</p>
          </div>

          <div className="w-full lg:w-1/2 order-2 lg:order-1 relative">
            <div className="card-3d hover-zone group">
              <div className="card-3d-inner">
                <Image
                  src="/frame-first.webp"
                  alt="Card Frame"
                  width={620}
                  height={431}
                  className="w-full h-auto transition-all duration-500 group-hover:shadow-2xl group-hover:shadow-[#19FB9B]/20"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 mt-24 max-w-[1200px]">
        <div className={`flex flex-col lg:flex-row items-center justify-between gap-12 transition-all duration-700 ${isVisible ? 'slide-in-right' : 'opacity-0'}`} style={{ animationDelay: '0.2s' }}>
          <div className="w-full lg:w-1/2 text-center lg:text-right hover-zone group">
            <h3 className="text-white text-[18px] sm:text-[24px] md:text-3xl font-bold mb-4 group-hover:text-[#19FB9B] transition-colors duration-300">Review Predictions</h3>
            <p className="text-[#9ca2b5] text-[14px] sm:text-[16px] md:text-lg group-hover:text-white transition-colors duration-300">
              Compare moneyline odds, spreads,
              <br />
              and totals from our single-model AI.
            </p>
          </div>

          <div className="w-full lg:w-1/2 order-2 lg:order-1 relative">
            <div className="card-3d hover-zone group">
              <div className="card-3d-inner">
                <Image
                  src="/frame-second.svg"
                  alt="Card Frame"
                  width={620}
                  height={431}
                  className="w-full h-auto transition-all duration-500 group-hover:shadow-2xl group-hover:shadow-[#8C01FA]/20"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 mt-24 max-w-[1200px]">
        <div className={`flex flex-col lg:flex-row items-center justify-between gap-12 transition-all duration-700 ${isVisible ? 'slide-in-left' : 'opacity-0'}`} style={{ animationDelay: '0.4s' }}>
          <div className="w-full lg:w-1/2 text-center lg:text-left order-1 lg:order-2 hover-zone group">
            <h3 className="text-white text-[18px] sm:text-[24px] md:text-3xl font-bold mb-4 group-hover:text-[#19FB9B] transition-colors duration-300">Leverage AI Confidence</h3>
            <p className="text-[#9ca2b5] text-[14px] sm:text-[16px] md:text-lg group-hover:text-white transition-colors duration-300">See how strongly our system supports each pick,
              <br />
              reducing uncertainty in your decisions.</p>
          </div>
          <div className="w-full lg:w-1/2 order-2 lg:order-1 relative">
            <div className="card-3d hover-zone group">
              <div className="card-3d-inner">
                <Image
                  src="/frame-third.svg"
                  alt="Card Frame"
                  width={620}
                  height={431}
                  className="w-full h-auto transition-all duration-500 group-hover:shadow-2xl group-hover:shadow-[#2CAD9C]/20"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={`container mx-auto px-4 mt-24 text-center max-w-[1200px] transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`} style={{ animationDelay: '0.6s' }}>
        <h2 className="text-white text-[18px] sm:text-2xl md:text-4xl font-bold mb-8 hover-zone">See the Data Behind Our Picks!</h2>
        <div className="flex flex-col gap-4 md:hidden w-full max-w-md mx-auto">
          <button className="magnetic-button liquid-button bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[16px] font-medium rounded-full w-full h-[56px] flex items-center justify-center relative overflow-hidden group">
            <span className="relative z-10">Explore AI Insights</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
        <div className="hidden md:flex flex-row items-center justify-center gap-4">
          <button className="magnetic-button liquid-button bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[14px] font-medium rounded-[88px] w-[224px] h-[48px] flex items-center justify-center relative overflow-hidden group">
            <span className="relative z-10">Explore AI Insights</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
      </div>
    </section>
  )
}












