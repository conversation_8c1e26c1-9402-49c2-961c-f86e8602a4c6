"use client"

import { useEffect, useState } from "react"

export default function CTABanner() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const element = document.querySelector('#cta-banner')
    if (element) {
      observer.observe(element)
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [])

  return (
    <section
      id="cta-banner"
      className="w-full py-16 md:py-24 relative overflow-hidden gradient-wave"
      style={{
        background: "linear-gradient(-45deg, #1F16279C, #2A1B3D9C, #1F16279C, #2A1B3D9C)",
        backgroundSize: "400% 400%",
        borderWidth: "2px 0px 2px 0px",
        borderStyle: "solid",
        borderImageSource: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
        borderImageSlice: "1",
        backdropFilter: "blur(44px)",
        WebkitBackdropFilter: "blur(44px)",
      }}
    >
      <div
        className="absolute opacity-30 blur-[100px] parallax-element parallax-slow"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "24.45px",
          left: "46px",
          background: "linear-gradient(45deg, #7F20EF, #8C01FA)",
          transform: "rotate(15.44deg)",
          zIndex: "0",
          animation: "floatBlur1 10s ease-in-out infinite, gradientWave 18s ease infinite",
        }}
      />

      <div
        className="absolute opacity-30 blur-[100px] parallax-element parallax-fast"
        style={{
          width: "362.36px",
          height: "252.34px",
          bottom: "24.45px",
          right: "46px",
          background: "linear-gradient(45deg, #2CAD9C, #19FB9B)",
          transform: "rotate(15.44deg)",
          zIndex: "0",
          animation: "floatBlur2 12s ease-in-out infinite, gradientWave 22s ease infinite",
        }}
      />

      <div className="w-full container mx-auto text-center px-4 relative z-10">
        <h2 className={`text-3xl md:text-4xl lg:text-[48px] font-bold mb-6 text-white leading-normal md:leading-relaxed hover-zone transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`}>
          Elevate Your Sports Knowledge –<br className="hidden xl:block md:block" /> DarkHorse<br className="xl:hidden md:hidden" /> &nbsp;
          <span className="gradient-text-animated hover:scale-105 transition-transform duration-300 cursor-default">
            Your Picks Today!
          </span>
        </h2>

        <p className={`text-white text-lg md:text-xl mb-10 hover-zone transition-all duration-700 ${isVisible ? 'text-reveal-delay-1' : 'opacity-0'}`}>
          Real-Time Insights <span className="mx-2 text-[#19FB9B]">|</span> Proven Accuracy <span className="mx-2 text-[#19FB9B]">|</span> No
          Bookmaker Bias
        </p>

        <div className={`flex flex-col sm:flex-row items-center justify-center gap-4 w-full transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`} style={{ animationDelay: '0.3s' }}>
          <button className="magnetic-button bg-transparent border border-white/20 text-white text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[48px] flex items-center justify-center hover:bg-white/10 hover:border-[#19FB9B]/50 transition-all duration-300 relative overflow-hidden group">
            <span className="relative z-10">Choose a Plan</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#19FB9B]/10 to-[#8C01FA]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
          <button className="magnetic-button liquid-button bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[48px] flex items-center justify-center relative overflow-hidden group">
            <span className="relative z-10">See AI Predictions</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
      </div>
    </section>
  )
}




